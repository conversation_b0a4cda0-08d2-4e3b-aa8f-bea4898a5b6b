package model

import (
	"time"
)

// ChannelStatistics 渠道统计数据模型
type ChannelStatistics struct {
	ID                     uint       `json:"id" gorm:"primaryKey;autoIncrement" db:"id"`                         // 主键ID，自增
	ChannelID              uint       `json:"channel_id" db:"channel_id"`                                         // 渠道ID，关联channel表
	NewCustomerRegNum      uint       `json:"new_customer_reg_num" db:"new_customer_reg_num"`                     // 新用户注册数
	RealNameNum            uint       `json:"real_name_num" db:"real_name_num"`                                   // 实名人数
	NumberOfTransactions   uint       `json:"number_of_transactions" db:"number_of_transactions"`                 // 成交数
	CreatedAt              *time.Time `json:"created_at" db:"created_at"`                                         // 创建时间
	DeletedAt              *time.Time `json:"deleted_at" db:"deleted_at"`                                         // 软删时间
}

// TableName 指定表名
func (ChannelStatistics) TableName() string {
	return "channel_statistics"
}

// ChannelStatisticsService 渠道统计服务
type ChannelStatisticsService struct {
	ctx context.Context
}

// NewChannelStatisticsService 创建渠道统计服务实例
func NewChannelStatisticsService(ctx context.Context) *ChannelStatisticsService {
	return &ChannelStatisticsService{
		ctx: ctx,
	}
}

// CreateOrUpdateStatistics 创建或更新渠道统计数据
// 同一渠道同一天的数据需要软删除上一条，然后创建新数据
func (s *ChannelStatisticsService) CreateOrUpdateStatistics(channelID uint, newCustomerRegNum, realNameNum, numberOfTransactions uint) error {
	now := time.Now()

	// 检查今天是否已有统计记录
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	tomorrow := today.Add(24 * time.Hour)

	existing, err := DB(WithContext(s.ctx)).Table("channel_statistics").
		Where("channel_id", channelID).
		Where("created_at >= ?", today).
		Where("created_at < ?", tomorrow).
		Where("deleted_at IS NULL").
		First()

	if err != nil && err.Error() != "record not found" {
		return fmt.Errorf("查询现有统计记录失败: %v", err)
	}

	if existing != nil {
		// 软删除现有记录
		_, err = DB(WithContext(s.ctx)).Table("channel_statistics").
			Where("id", existing["id"]).
			Update(map[string]interface{}{
				"deleted_at": now,
			})

		if err != nil {
			return fmt.Errorf("软删除现有统计记录失败: %v", err)
		}
	}

	// 创建新记录
	statistics := ChannelStatistics{
		ChannelID:             channelID,
		NewCustomerRegNum:     newCustomerRegNum,
		RealNameNum:           realNameNum,
		NumberOfTransactions:  numberOfTransactions,
		CreatedAt:             &now,
	}

	_, err = DB(WithContext(s.ctx)).Table("channel_statistics").Insert(statistics)
	if err != nil {
		return fmt.Errorf("创建渠道统计失败: %v", err)
	}

	return nil
}

// GetStatisticsByChannelAndDate 根据渠道ID和日期获取统计数据
func (s *ChannelStatisticsService) GetStatisticsByChannelAndDate(channelID uint, date time.Time) (*ChannelStatistics, error) {
	startOfDay := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, time.Local)
	endOfDay := startOfDay.Add(24 * time.Hour)
	
	result, err := DB(WithContext(s.ctx)).Table("channel_statistics").
		Where("channel_id", channelID).
		Where("created_at >= ?", startOfDay).
		Where("created_at < ?", endOfDay).
		Where("deleted_at IS NULL").
		First()
	
	if err != nil {
		if err.Error() == "record not found" {
			return nil, nil
		}
		return nil, fmt.Errorf("查询渠道统计失败: %v", err)
	}
	
	statistics := &ChannelStatistics{}
	if err := gconv.Struct(result, statistics); err != nil {
		return nil, fmt.Errorf("数据转换失败: %v", err)
	}
	
	return statistics, nil
}

// GetAllChannels 获取所有启用的渠道
func (s *ChannelStatisticsService) GetAllChannels() ([]map[string]interface{}, error) {
	result, err := DB(WithContext(s.ctx)).Table("channel").
		Where("channel_status", 1).
		Fields("id, channel_name, channel_code").
		Get()

	if err != nil {
		return nil, fmt.Errorf("查询渠道列表失败: %v", err)
	}

	// 转换为[]map[string]interface{}
	var channels []map[string]interface{}
	for _, item := range result {
		channels = append(channels, map[string]interface{}(item))
	}

	return channels, nil
}

// BatchCreateStatistics 批量创建统计数据
func (s *ChannelStatisticsService) BatchCreateStatistics(statisticsList []ChannelStatistics) error {
	if len(statisticsList) == 0 {
		return nil
	}

	for _, statistics := range statisticsList {
		err := s.CreateOrUpdateStatistics(
			statistics.ChannelID,
			statistics.NewCustomerRegNum,
			statistics.RealNameNum,
			statistics.NumberOfTransactions,
		)
		if err != nil {
			return fmt.Errorf("批量创建统计数据失败: %v", err)
		}
	}

	return nil
}
